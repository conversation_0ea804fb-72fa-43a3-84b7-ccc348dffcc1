{"seriesNum": "299", "PNR": "ALG5OR", "bookAgent": "MOBILE_APP", "resCurrency": "QAR", "PNRPin": "81892938", "bookDate": "2025-04-10T15:14:54", "modifyDate": "2025-05-10T17:04:59", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 3, "activeSegCount": 1, "webBookingID": "d3c929cf9782w9mlu444sds8taedn3m593051adf515c", "securityGUID": "d3c929cf9782w9mlu444sds8taedn3m593051adf515c", "lastLoadGUID": "b7e0f4ce-fe3e-48c2-b3c6-bfc15f8e17e7", "isAsyncPNR": false, "MasterPNR": "ALG5OR", "segments": [{"segKey": "16107349:16107349:5/13/2025 10:55:00 AM", "LFID": 16107349, "depDate": "2025-05-13T00:00:00", "flightGroupId": "16107349", "org": "DOH", "dest": "GYD", "depTime": "2025-05-13T10:55:00", "depTimeGMT": "2025-05-13T07:55:00", "arrTime": "2025-05-13T17:00:00", "operCarrier": "FZ", "operFlightNum": "004/707", "mrktCarrier": "FZ ", "mrktFlightNum": "004/707", "persons": [{"recNum": 8, "status": 5}, {"recNum": 9, "status": 5}, {"recNum": 7, "status": 5}], "legDetails": [{"PFID": 181009, "depDate": "2025-05-13T10:55:00", "legKey": "16107349:181009:5/13/2025 10:55:00 AM", "customerKey": "AAF5859E996A0ACEAE7BC37C28EF3E3D0D743E5C1F80411F268801550B247D74"}, {"PFID": 181216, "depDate": "2025-05-13T14:10:00", "legKey": "16107349:181216:5/13/2025 2:10:00 PM", "customerKey": "A4E1156672E6125924FA6DA32AA121DD95CD78B94F35A174867846AC29C70358"}], "active": true, "changeType": "AC"}, {"segKey": "16107349:16107349:5/22/2025 10:55:00 AM", "LFID": 16107349, "depDate": "2025-05-22T00:00:00", "flightGroupId": "16107349", "org": "DOH", "dest": "GYD", "depTime": "2025-05-22T10:55:00", "depTimeGMT": "2025-05-22T07:55:00", "arrTime": "2025-05-22T17:00:00", "operCarrier": "FZ", "operFlightNum": "004/707", "mrktCarrier": "FZ ", "mrktFlightNum": "004/707", "persons": [{"recNum": 1, "status": 0}, {"recNum": 3, "status": 0}, {"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181009, "depDate": "2025-05-22T10:55:00", "legKey": "16107349:181009:5/22/2025 10:55:00 AM", "customerKey": "38935258FB5B1C2D3D6B4BDF67BC7E2F9E81903CFC6640F0FF6CFF1A9784DFE2"}, {"PFID": 181216, "depDate": "2025-05-22T14:10:00", "legKey": "16107349:181216:5/22/2025 2:10:00 PM", "customerKey": "94D33C072B04CA32FEFA79D6BE50B1EE198460143544747C4AED621491271997"}], "active": true, "changeType": "AC"}, {"segKey": "16113055:16113055:6/12/2025 12:05:00 PM", "LFID": 16113055, "depDate": "2025-06-12T00:00:00", "flightGroupId": "16113055", "org": "GYD", "dest": "DOH", "depTime": "2025-06-12T12:05:00", "depTimeGMT": "2025-06-12T08:05:00", "arrTime": "2025-06-12T17:30:00", "operCarrier": "FZ", "operFlightNum": "710/009", "mrktCarrier": "FZ ", "mrktFlightNum": "710/009", "persons": [{"recNum": 4, "status": 1}, {"recNum": 5, "status": 1}, {"recNum": 6, "status": 1}], "legDetails": [{"PFID": 181226, "depDate": "2025-06-12T12:05:00", "legKey": "16113055:181226:6/12/2025 12:05:00 PM", "customerKey": "147A806031868E095A7CB9EA45BB02A7AC9F296B609670AC577B3D09DE02D0F2"}, {"PFID": 181011, "depDate": "2025-06-12T17:20:00", "legKey": "16113055:181011:6/12/2025 5:20:00 PM", "customerKey": "4F6F700ED6F7F4889604E045FE57E72DD0E69DD2F3CAA6E109C54DC8CFA92A2D"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": *********, "fName": "IRADA", "lName": "ALIYEVA", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 4, 8]}, {"paxID": *********, "fName": "NARINJ", "lName": "MAMMADOVA", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [3, 5, 9]}, {"paxID": *********, "fName": "DIANA", "lName": "GOZZO", "title": "MISS", "PTCID": 5, "gender": "F", "DOB": "2024-05-07T00:00:00", "recNum": [2, 6, 7]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "4/10/2025 3:10:41 PM", "provider": "AIG", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "*********", "insuTransID": "dca1899d-a340-4185-9df9-c72aa0abf6ad", "toRecNum": 8, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "67f7de10000778000001415a#2#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-10T15:14:54"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "R", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "toRecNum": 7, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "67f7de10000778000001415a#3#1#MOBILE#VAYANT#CREATE", "travelsWithPaxID": "*********", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-10T15:14:54"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "4/10/2025 3:10:41 PM", "provider": "AIG", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "*********", "insuTransID": "dca1899d-a340-4185-9df9-c72aa0abf6ad", "toRecNum": 9, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "67f7de10000778000001415a#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-10T15:14:54"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "T", "status": 1, "fareClass": "T", "operFareClass": "T", "FBC": "TRB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "U8DNY-<PERSON>V<PERSON>R-INS/b3139c48-f75b-4439-966f-88b3dbdf6869", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681f86a80007780000009f58#*********#2#MOBILE#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-10T15:14:54"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "T", "status": 1, "fareClass": "T", "operFareClass": "T", "FBC": "TRB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "U8DNY-<PERSON>V<PERSON>R-INS/b3139c48-f75b-4439-966f-88b3dbdf6869", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f86a80007780000009f58#*********#2#MOBILE#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-10T15:14:54"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "T", "status": 1, "fareClass": "T", "operFareClass": "T", "FBC": "TRB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681f86a80007780000009f58#*********#2#MOBILE#OneSearch#CHANGE", "travelsWithPaxID": "*********", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-10T15:14:54"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "R", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7QA5", "fareBrand": "Value", "cabin": "ECONOMY", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681f86a80007780000009f58#*********#1#MOBILE#OneSearch#CHANGE", "travelsWithPaxID": "*********", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-05-10T17:04:11"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "R", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7QA5", "fareBrand": "Value", "cabin": "ECONOMY", "emergencyContactID": *********, "discloseEmergencyContact": 1, "insuTransID": "U8DNY-<PERSON>V<PERSON>R-INS/b3139c48-f75b-4439-966f-88b3dbdf6869", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681f86a80007780000009f58#*********#1#MOBILE#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-05-10T17:04:11"}]}, {"recNum": 9, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "R", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7QA5", "fareBrand": "Value", "cabin": "ECONOMY", "emergencyContactID": *********, "discloseEmergencyContact": 1, "insuTransID": "U8DNY-<PERSON>V<PERSON>R-INS/b3139c48-f75b-4439-966f-88b3dbdf6869", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f86a80007780000009f58#*********#1#MOBILE#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-05-10T17:04:11"}]}], "payments": [{"paymentID": *********, "paxID": 268531586, "method": "IPAY", "status": "1", "paidDate": "2025-05-10T17:04:52", "cardNum": "************4266", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 635.45, "baseCurr": "QAR", "baseAmt": 635.45, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "Ing. <PERSON>", "authCode": "027988", "reference": "22957000", "externalReference": "22957000", "tranId": "21327379", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21327379}, {"paymentID": *********, "paxID": 265376787, "method": "MSCD", "status": "1", "paidDate": "2025-04-10T15:15:41", "cardNum": "************6501", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 1396.79, "baseCurr": "QAR", "baseAmt": 5459, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "H52202", "reference": "22325518", "externalReference": "22325518", "tranId": "20704641", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "MCCMPGSPEEUR", "exchangeRate": "0.25586906", "resExternalPaymentID": 20704641}], "OAFlights": null, "physicalFlights": [{"key": "16107349:181009:2025-05-13T10:55:00 AM", "LFID": 16107349, "PFID": 181009, "org": "DOH", "dest": "DXB", "depDate": "2025-05-13T10:55:00", "depTime": "2025-05-13T10:55:00", "arrTime": "2025-05-13T13:10:00", "carrier": "FZ", "flightNum": "004", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73B", "mrktCarrier": "FZ", "mrktFlightNum": "004", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:42 AM"}, {"key": "16107349:181216:2025-05-13T02:10:00 PM", "LFID": 16107349, "PFID": 181216, "org": "DXB", "dest": "GYD", "depDate": "2025-05-13T14:10:00", "depTime": "2025-05-13T14:10:00", "arrTime": "2025-05-13T17:00:00", "carrier": "FZ", "flightNum": "707", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "707", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "GYD", "operatingCarrier": "FZ", "flightDuration": 10200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Baku", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:42 AM"}, {"key": "16107349:181009:2025-05-22T10:55:00 AM", "LFID": 16107349, "PFID": 181009, "org": "DOH", "dest": "DXB", "depDate": "2025-05-22T10:55:00", "depTime": "2025-05-22T10:55:00", "arrTime": "2025-05-22T13:10:00", "carrier": "FZ", "flightNum": "004", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "004", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:42 AM"}, {"key": "16107349:181216:2025-05-22T02:10:00 PM", "LFID": 16107349, "PFID": 181216, "org": "DXB", "dest": "GYD", "depDate": "2025-05-22T14:10:00", "depTime": "2025-05-22T14:10:00", "arrTime": "2025-05-22T17:00:00", "carrier": "FZ", "flightNum": "707", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "707", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "GYD", "operatingCarrier": "FZ", "flightDuration": 10200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Baku", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:42 AM"}, {"key": "16113055:181226:2025-06-12T12:05:00 PM", "LFID": 16113055, "PFID": 181226, "org": "GYD", "dest": "DXB", "depDate": "2025-06-12T12:05:00", "depTime": "2025-06-12T12:05:00", "arrTime": "2025-06-12T15:00:00", "carrier": "FZ", "flightNum": "710", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "710", "flightStatus": "OPEN", "originMetroGroup": "GYD", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 10500, "reaccomChangeAlert": false, "originName": "Baku", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "5/6/2025 8:35:35 AM"}, {"key": "16113055:181011:2025-06-12T05:20:00 PM", "LFID": 16113055, "PFID": 181011, "org": "DXB", "dest": "DOH", "depDate": "2025-06-12T17:20:00", "depTime": "2025-06-12T17:20:00", "arrTime": "2025-06-12T17:30:00", "carrier": "FZ", "flightNum": "009", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "009", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": true, "changeType": "AC", "flightChangeTime": "5/6/2025 8:35:35 AM"}], "chargeInfos": [{"recNum": 3, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-10T15:14:54", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-10T15:14:53"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "QAR", "originalAmt": -190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213679, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1340368496, "codeType": "TAX", "taxID": 6307, "taxCode": "JL", "taxChargeID": **********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Flight Security tax", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213681, "paymentMap": [{"key": "1340368496:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1340368499, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213677, "paymentMap": [{"key": "1340368499:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1340368494, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213682, "paymentMap": [{"key": "1340368494:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1340368502, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213676, "paymentMap": [{"key": "1340368502:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1340368500, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213680, "paymentMap": [{"key": "1340368500:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1340368501, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213678, "paymentMap": [{"key": "1340368501:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1300213682, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213682:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213679, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213679:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213681, "codeType": "TAX", "taxID": 6307, "taxCode": "JL", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Flight Security tax", "comment": "Flight Security tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213681:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1300213677, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213677:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1300213676, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213676:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1300213678, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213678:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213680, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213680:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1340368497, "codeType": "AIR", "amt": -555, "curr": "QAR", "originalAmt": -555, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-10T17:04:12", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1340368497:*********", "paymentID": *********, "amt": -555, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 555, "curr": "QAR", "originalAmt": 555, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-10T15:14:54", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 555, "approveCode": 0}]}, {"chargeID": 1300219615, "codeType": "PMNT", "amt": 159, "curr": "QAR", "originalAmt": 159, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-10T15:15:48", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300219615:*********", "paymentID": *********, "amt": 159, "approveCode": 0}]}, {"chargeID": 1340368503, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T17:04:12", "desc": "Penalty AddedDueToModify FZ  004 DOH  - DXB  22-May-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368503:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1300213784, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1300213832, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181009"}, {"chargeID": 1300213831, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181216"}]}, {"recNum": 5, "charges": [{"chargeID": 1340368652, "codeType": "INSU", "amt": 69.74, "curr": "QAR", "originalAmt": 69.74, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-10T17:04:12", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368652:*********", "paymentID": *********, "amt": 69.74, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"76.61\",\"Tax\":\"3.65\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-10T17:04:12"}, {"chargeID": **********, "codeType": "INSU", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-10T15:14:54", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "parameter2Name": "PROVIDER", "parameter2Value": "AIG"}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 6524, "taxCode": "AZ", "taxChargeID": **********, "amt": 80, "curr": "QAR", "originalAmt": 80, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Departure tax", "comment": "Departure tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1300213794, "codeType": "TAX", "taxID": 10326, "taxCode": "KD", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "CAA Regulation Tax", "comment": "CAA Regulation Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213794:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213789, "codeType": "TAX", "taxID": 6307, "taxCode": "JL", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Flight Security tax", "comment": "Flight Security tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213789:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1300213787, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213787:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213790, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213790:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213791, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213791:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1300213793, "codeType": "TAX", "taxID": 6224, "taxCode": "JB", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Airport devolopment tax", "comment": "Airport devolopment tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213793:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 975, "curr": "QAR", "originalAmt": 975, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-10T15:14:54", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 975, "approveCode": 0}]}, {"chargeID": 1300213795, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1300213833, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181226"}, {"chargeID": 1300213834, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181011"}]}, {"recNum": 9, "charges": [{"chargeID": 1340368650, "codeType": "INSU", "amt": 69.73, "curr": "QAR", "originalAmt": 69.73, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-10T17:04:13", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368650:*********", "paymentID": *********, "amt": 69.73, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"76.61\",\"Tax\":\"3.65\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-10T17:04:13"}, {"chargeID": 1340368549, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1340368546, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368549:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340368553, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340368546, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368553:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340368550, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1340368546, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368550:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340368547, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1340368546, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368547:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340368554, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340368546, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368554:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1340368548, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1340368546, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368548:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340368551, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1340368546, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368551:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1340368552, "codeType": "TAX", "taxID": 6307, "taxCode": "JL", "taxChargeID": 1340368546, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Flight Security tax", "comment": "Flight Security tax", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368552:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1340368546, "codeType": "AIR", "amt": 565, "curr": "QAR", "originalAmt": 565, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T17:04:12", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368546:*********", "paymentID": *********, "amt": 338, "approveCode": 0}, {"key": "1340368546:*********", "paymentID": *********, "amt": 227, "approveCode": 0}]}, {"chargeID": 1340371763, "codeType": "PMNT", "amt": 18.51, "curr": "QAR", "originalAmt": 18.51, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T17:04:58", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340371763:*********", "paymentID": *********, "amt": 18.51, "approveCode": 0}]}, {"chargeID": 1340368555, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1340368546, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1340368565, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181216"}, {"chargeID": 1340368564, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181009"}]}, {"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-10T15:14:54", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-10T15:14:53"}, {"chargeID": **********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:11", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:11", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213801, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1340368485, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:11", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213798, "paymentMap": [{"key": "1340368485:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1340368492, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:11", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213802, "paymentMap": [{"key": "1340368492:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1340368490, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:11", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213804, "paymentMap": [{"key": "1340368490:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1340368486, "codeType": "TAX", "taxID": 6307, "taxCode": "JL", "taxChargeID": **********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:11", "desc": "Flight Security tax", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213803, "paymentMap": [{"key": "1340368486:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1340368484, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:11", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213799, "paymentMap": [{"key": "1340368484:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1340368487, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "QAR", "originalAmt": -190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T17:04:11", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213805, "paymentMap": [{"key": "1340368487:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1300213798, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213798:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1300213799, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213799:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1300213803, "codeType": "TAX", "taxID": 6307, "taxCode": "JL", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Flight Security tax", "comment": "Flight Security tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213803:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1300213805, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213805:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213802, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213802:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1300213804, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213804:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213801, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213801:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340368489, "codeType": "AIR", "amt": -555, "curr": "QAR", "originalAmt": -555, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-10T17:04:11", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1340368489:*********", "paymentID": *********, "amt": -555, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 555, "curr": "QAR", "originalAmt": 555, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-10T15:14:54", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 555, "approveCode": 0}]}, {"chargeID": 1340368493, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T17:04:11", "desc": "Penalty AddedDueToModify FZ  004 DOH  - DXB  22-May-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368493:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1300213806, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1300213835, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181216"}, {"chargeID": 1300213836, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181009"}]}, {"recNum": 4, "charges": [{"chargeID": 1340368653, "codeType": "INSU", "amt": 69.74, "curr": "QAR", "originalAmt": 69.74, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-10T17:04:12", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368653:*********", "paymentID": *********, "amt": 69.74, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"76.61\",\"Tax\":\"3.65\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-10T17:04:12"}, {"chargeID": **********, "codeType": "INSU", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-10T15:14:54", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "parameter2Name": "PROVIDER", "parameter2Value": "AIG"}, {"chargeID": **********, "codeType": "TAX", "taxID": 6307, "taxCode": "JL", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Flight Security tax", "comment": "Flight Security tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10326, "taxCode": "KD", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "CAA Regulation Tax", "comment": "CAA Regulation Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213815, "codeType": "TAX", "taxID": 6224, "taxCode": "JB", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Airport devolopment tax", "comment": "Airport devolopment tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213815:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1300213809, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213809:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1300213813, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213813:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1300213810, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213810:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1300213814, "codeType": "TAX", "taxID": 6524, "taxCode": "AZ", "taxChargeID": **********, "amt": 80, "curr": "QAR", "originalAmt": 80, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Departure tax", "comment": "Departure tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213814:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1300213812, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213812:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 975, "curr": "QAR", "originalAmt": 975, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-10T15:14:54", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 975, "approveCode": 0}]}, {"chargeID": 1300213817, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1300213837, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181226"}, {"chargeID": 1300213838, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181011"}]}, {"recNum": 8, "charges": [{"chargeID": 1340368651, "codeType": "INSU", "amt": 69.73, "curr": "QAR", "originalAmt": 69.73, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-10T17:04:12", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368651:*********", "paymentID": *********, "amt": 69.73, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"76.61\",\"Tax\":\"3.65\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-10T17:04:12"}, {"chargeID": 1340368532, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1340368531, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368532:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340368535, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1340368531, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368535:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340368536, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1340368531, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368536:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1340368534, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1340368531, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368534:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340368538, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340368531, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368538:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340368533, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1340368531, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368533:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340368539, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340368531, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368539:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1340368537, "codeType": "TAX", "taxID": 6307, "taxCode": "JL", "taxChargeID": 1340368531, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Flight Security tax", "comment": "Flight Security tax", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368537:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1340368531, "codeType": "AIR", "amt": 565, "curr": "QAR", "originalAmt": 565, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T17:04:12", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368531:*********", "paymentID": *********, "amt": 565, "approveCode": 0}]}, {"chargeID": 1340368540, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1340368531, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1340368562, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181009"}, {"chargeID": 1340368563, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181216"}]}, {"recNum": 2, "charges": [{"chargeID": 1340368483, "codeType": "AIR", "amt": -170, "curr": "QAR", "originalAmt": -170, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-10T17:04:12", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1300213819, "paymentMap": [{"key": "1340368483:*********", "paymentID": *********, "amt": -170, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-10T17:04:12"}, {"chargeID": 1300213819, "codeType": "AIR", "amt": 170, "curr": "QAR", "originalAmt": 170, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-10T15:14:54", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213819:*********", "paymentID": *********, "amt": 170, "approveCode": 0}]}, {"chargeID": 1300213820, "codeType": "BAGI", "taxID": 10314, "taxCode": "BAGI", "taxChargeID": 1300213819, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "10kg baggage INCLUDED in fare", "comment": "10kg baggage INCLUDED in fare", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 6, "charges": [{"chargeID": 1300213821, "codeType": "AIR", "amt": 170, "curr": "QAR", "originalAmt": 170, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-10T15:14:54", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1300213821:*********", "paymentID": *********, "amt": 170, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-10T15:14:53"}, {"chargeID": 1300213822, "codeType": "BAGI", "taxID": 10314, "taxCode": "BAGI", "taxChargeID": 1300213821, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-10T15:14:54", "desc": "10kg baggage INCLUDED in fare", "comment": "10kg baggage INCLUDED in fare", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 7, "charges": [{"chargeID": 1340368465, "codeType": "AIR", "amt": 170, "curr": "QAR", "originalAmt": 170, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T17:04:12", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340368465:*********", "paymentID": *********, "amt": 170, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-10T17:04:12"}, {"chargeID": 1340368466, "codeType": "BAGI", "taxID": 10314, "taxCode": "BAGI", "taxChargeID": 1340368465, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-10T17:04:12", "desc": "10kg baggage INCLUDED in fare", "comment": "10kg baggage INCLUDED in fare", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}], "parentPNRs": [], "childPNRs": []}